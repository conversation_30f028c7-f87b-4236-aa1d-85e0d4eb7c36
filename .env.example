# Authentication
VITE_AUTH_REQUIRED=true

# Supabase config
VITE_SUPABASE_URL=""
VITE_SUPABASE_ANON_KEY=""

# AI
VITE_DEEPSEEK_API_KEY=sk-
VITE_DEEPSEEK_API_URL=https://api.deepseek.com/v1
VITE_DEEPSEEK_API_MODEL=deepseek-reasoner

# Solana
VITE_HELIUS_API_KEY=""
VITE_WALLET_SIGNATURE_TEMPLATE="# Chain Fox Wallet Connection\n\nI, the owner of wallet address {{address}}, hereby confirm that I am securely connecting to <PERSON> Fox (www.chain-fox.com).\n\nThis signature verifies my ownership of this wallet and authorizes this connection.\n\n---\nTimestamp: {{timestamp}}\nNetwork: Solana {{network}}\nWebsite: www.chain-fox.com"

# SPL Token Program IDs
VITE_TOKEN_PROGRAM_ID=TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA
VITE_ASSOCIATED_TOKEN_PROGRAM_ID=ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL
