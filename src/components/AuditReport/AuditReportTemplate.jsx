import React from 'react';
import { useTranslation } from 'react-i18next';
import logo from '/public/logo.png';

/**
 * Audit Report Template Component
 * This component renders a professional security audit report based on the provided data
 * It is designed to be exported as PDF
 */
const AuditReportTemplate = React.forwardRef(({ data }, ref) => {
  const { t } = useTranslation();
  const {
    scanId,
    timestamp,
    target,
    vulnerabilities,
    metrics,
    summary,
    codeContent,
    thinking
  } = data;

  // Format date for better readability
  const formattedDate = new Date(timestamp).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  // Count vulnerabilities by severity
  const severityCounts = {
    critical: vulnerabilities.filter(v => v.severity === 'critical').length,
    high: vulnerabilities.filter(v => v.severity === 'high').length,
    medium: vulnerabilities.filter(v => v.severity === 'medium').length,
    low: vulnerabilities.filter(v => v.severity === 'low').length,
    info: vulnerabilities.filter(v => v.severity === 'info' || v.severity === 'informational').length
  };

  // Get severity color class
  const getSeverityColorClass = (severity) => {
    switch (severity) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div ref={ref} className="p-8 bg-white text-black font-sans max-w-4xl mx-auto">
      {/* Report Header */}
      <div className="flex justify-between items-center border-b border-gray-300 pb-6 mb-6">
        <div className="flex items-center">
          <img src={logo} alt="Chain Fox Logo" className="h-12 mr-4" />
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              {t('report.title', 'Self-Service Audit Report')}
            </h1>
            <p className="text-gray-600">{formattedDate}</p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-600">{t('report.scanId', 'Scan ID')}: {scanId}</p>
          <p className="text-sm text-gray-600">{t('report.generatedBy', 'Generated by')}: Chain Fox</p>
        </div>
      </div>

      {/* Executive Summary */}
      <section className="mb-8">
        <h2 className="text-xl font-bold mb-4 text-gray-800 border-b border-gray-200 pb-2">
          {t('report.executiveSummary', 'Executive Summary')}
        </h2>
        <p className="mb-4 text-gray-700">{summary}</p>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h3 className="font-bold mb-2 text-gray-800">{t('report.target', 'Target')}</h3>
          <p className="text-gray-700 font-mono text-sm">{target}</p>
        </div>
      </section>

      {/* Security Metrics */}
      <section className="mb-8">
        <h2 className="text-xl font-bold mb-4 text-gray-800 border-b border-gray-200 pb-2">
          {t('report.securityMetrics', 'Security Metrics')}
        </h2>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          {Object.entries(metrics).map(([key, value]) => {
            if (key === 'scanDuration') return null;

            // Determine color based on score
            let colorClass = 'text-gray-800';
            if (value >= 90) colorClass = 'text-green-600';
            else if (value >= 70) colorClass = 'text-blue-600';
            else if (value >= 50) colorClass = 'text-yellow-600';
            else colorClass = 'text-red-600';

            return (
              <div key={key} className="bg-gray-100 p-4 rounded-lg text-center">
                <div className={`text-2xl font-bold ${colorClass}`}>
                  {t('common.score', 'Score')}: {value}%
                </div>
                <div className="text-sm text-gray-600 mt-1 capitalize">
                  {t(`detectionPage.metrics.${key}`)}
                </div>
              </div>
            );
          })}
        </div>

        {/* Vulnerability Distribution */}
        <div className="mb-6">
          <h3 className="font-bold mb-3 text-gray-800">{t('report.vulnerabilityDistribution', 'Vulnerability Distribution')}</h3>
          <div className="grid grid-cols-5 gap-2 bg-gray-100 p-4 rounded-lg">
            {Object.entries(severityCounts).map(([severity, count]) => (
              <div key={severity} className="text-center">
                <div className={`text-lg font-bold ${getSeverityColorClass(severity)}`}>
                  {count}
                </div>
                <div className="text-xs text-gray-600 capitalize">{severity}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Vulnerabilities */}
      <section className="mb-8">
        <h2 className="text-xl font-bold mb-4 text-gray-800 border-b border-gray-200 pb-2">
          {t('report.vulnerabilities', 'Vulnerabilities')}
        </h2>

        {vulnerabilities.length === 0 ? (
          <p className="text-green-600 font-medium">{t('report.noVulnerabilities', 'No vulnerabilities detected.')}</p>
        ) : (
          <div className="space-y-6">
            {vulnerabilities.map((vuln, index) => (
              <div key={vuln.id} className="border border-gray-200 rounded-lg overflow-hidden">
                <div className={`p-4 flex justify-between items-start ${
                  vuln.severity === 'critical' ? 'bg-red-50' :
                  vuln.severity === 'high' ? 'bg-orange-50' :
                  vuln.severity === 'medium' ? 'bg-yellow-50' :
                  vuln.severity === 'low' ? 'bg-blue-50' :
                  'bg-gray-50'
                }`}>
                  <div>
                    <h3 className="font-bold text-gray-800">
                      {index + 1}. {vuln.title || vuln.name}
                    </h3>
                    <p className="text-sm text-gray-600">{t('report.location', 'Location')}: {vuln.location}</p>
                  </div>
                  <div className={`px-2 py-1 rounded text-xs font-medium ${
                    vuln.severity === 'critical' ? 'text-white bg-red-600' :
                    vuln.severity === 'high' ? 'text-white bg-orange-600' :
                    vuln.severity === 'medium' ? 'text-white bg-yellow-600' :
                    vuln.severity === 'low' ? 'text-white bg-blue-600' :
                    'text-white bg-gray-600'
                  }`}>
                    {vuln.severity.toUpperCase()}
                  </div>
                </div>

                <div className="p-4 bg-white">
                  <div className="mb-3">
                    <h4 className="font-medium text-gray-800">{t('report.description', 'Description')}:</h4>
                    <p className="text-gray-700">{vuln.description}</p>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-800">{t('report.recommendation', 'Recommendation')}:</h4>
                    <p className="text-gray-700">{vuln.recommendation}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </section>

      {/* Code Snippet */}
      {codeContent && (
        <section className="mb-8">
          <h2 className="text-xl font-bold mb-4 text-gray-800 border-b border-gray-200 pb-2">
            {t('report.analyzedCode', 'Analyzed Code')}
          </h2>
          <div className="bg-gray-100 p-4 rounded-lg overflow-auto">
            <pre className="text-sm font-mono whitespace-pre-wrap text-gray-800">
              {codeContent}
            </pre>
          </div>
        </section>
      )}

      {/* AI Analysis Process (Optional) */}
      {thinking && thinking.length > 0 && (
        <section className="mb-8">
          <h2 className="text-xl font-bold mb-4 text-gray-800 border-b border-gray-200 pb-2">
            {t('report.aiAnalysis', 'AI Analysis Process')}
          </h2>
          <div className="bg-gray-100 p-4 rounded-lg">
            {thinking.map((step, index) => (
              <div key={index} className="mb-4 border-l-2 border-purple-400 pl-3 py-1">
                <p className="text-sm text-gray-700">{step}</p>
              </div>
            ))}
          </div>
          <p className="text-sm text-gray-500 mt-2 italic">
            {t('report.aiDisclaimer', 'This is a preliminary AI-powered audit. For critical projects, we recommend a full professional audit.')}
          </p>
        </section>
      )}

      {/* Footer */}
      <footer className="mt-12 pt-6 border-t border-gray-300 text-center text-sm text-gray-600">
        <p>{t('report.disclaimer', 'This report is provided for informational purposes only and does not constitute professional advice. Chain Fox is not liable for any damages arising from the use of this report.')}</p>
        <p className="mt-2">© {new Date().getFullYear()} Chain Fox. {t('report.allRightsReserved', 'All rights reserved.')}</p>
      </footer>
    </div>
  );
});

export default AuditReportTemplate;
