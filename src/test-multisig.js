/**
 * 测试多签服务
 */

import multisigService, { createAdminWithdrawProposal, MULTISIG_SIGNERS } from './services/multisigService.js';

async function testMultisigService() {
  console.log('🧪 开始测试多签服务...');

  try {
    // 测试获取多签配置
    console.log('📋 测试获取多签配置...');
    const config = await multisigService.getMultisigConfig();
    console.log('✅ 多签配置:', config);

    // 测试获取所有提案
    console.log('📝 测试获取所有提案...');
    const proposals = await multisigService.getAllProposals();
    console.log('✅ 提案列表:', proposals);

    // 测试检查多签成员
    console.log('👥 测试检查多签成员...');
    const testAddress = 'So11111111111111111111111111111111111111112'; // 测试地址
    const isSigner = multisigService.isMultisigSigner(testAddress);
    console.log(`✅ 地址 ${testAddress} 是否为多签成员:`, isSigner);

    console.log('🎉 多签服务测试完成！');

  } catch (error) {
    console.error('❌ 多签服务测试失败:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
  }
}

// 测试指令数据生成
async function testInstructionCreation() {
  console.log('🧪 测试多签指令数据生成...');

  try {
    // 使用第一个多签成员地址进行测试
    const testWalletAddress = MULTISIG_SIGNERS[0];
    console.log('测试钱包地址:', testWalletAddress);
    console.log('所有多签成员:', MULTISIG_SIGNERS);

    // 测试创建管理员提取提案指令
    const testAmount = 1000000; // 1 CFX (6 decimals)
    const testRecipient = 'DJmqhERPWgaRfN3FPCJRtz3hARN8EXHUtn9ppjK2Hn6o'; // 使用多签成员地址作为接收者

    console.log('创建测试提案指令...');
    const instruction = await createAdminWithdrawProposal({
      walletAddress: testWalletAddress,
      amount: testAmount,
      recipientAddress: testRecipient
    });

    console.log('✅ 指令创建成功!');
    console.log('指令数据长度:', instruction.data.length);
    console.log('指令数据 (hex):', Array.from(instruction.data).map(b => b.toString(16).padStart(2, '0')).join(' '));
    console.log('程序 ID:', instruction.programId.toString());
    console.log('账户数量:', instruction.keys.length);

    // 显示账户信息
    instruction.keys.forEach((key, index) => {
      console.log(`账户 ${index}:`, {
        pubkey: key.pubkey.toString(),
        isSigner: key.isSigner,
        isWritable: key.isWritable
      });
    });

  } catch (error) {
    console.error('❌ 指令创建测试失败:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
  }
}

// 在浏览器控制台中运行测试
if (typeof window !== 'undefined') {
  window.testMultisigService = testMultisigService;
  window.testInstructionCreation = testInstructionCreation;
  console.log('💡 在浏览器控制台中运行以下命令来测试:');
  console.log('  - testMultisigService() 测试多签服务');
  console.log('  - testInstructionCreation() 测试指令数据生成');
}

export default testMultisigService;
export { testInstructionCreation };
