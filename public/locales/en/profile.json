{"title": "Profile", "subtitle": "Manage your account information and preferences", "email": "Email", "name": "Name", "avatar": "Avatar", "personalInfo": "Personal Information", "preferences": "Preferences", "language": "Language", "provider": "<PERSON>gin Provider", "walletInfo": "Wallet Information", "address": "Address", "balance": "Balance", "refreshBalance": "Refresh Balance", "walletAddress": "Wallet Address", "wallet": {"connect": "Connect Solana Wallet", "disconnect": "Disconnect Wallet", "connectDesc": "Connect your Solana wallet to access additional features", "connected": "Wallet Connected", "notConnected": "Wallet Not Connected", "connectionError": "Failed to connect wallet", "disconnectionError": "Failed to disconnect wallet", "secureConnection": "Secure connection verified", "signatureWarning": "Connected without signature verification", "signMessage": "Please sign the message to verify wallet ownership", "signatureSuccess": "Signature verified successfully", "signatureFailure": "Signature verification failed", "credits": {"title": "Wallet Credits", "available": "Available Credits", "sourceWallet": "Source Wallet Address", "targetWallet": "Target Wallet Address", "loadError": "Failed to load wallet credits", "transfer": {"title": "Transfer Credits from Wallet", "show": "Wallet Credits Transfer", "hide": "Hide Transfer Form", "targetWallet": "Target Wallet Address", "targetWalletPlaceholder": "Enter wallet address", "amount": "Amount", "amountPlaceholder": "Enter amount", "description": "Description (Optional)", "descriptionPlaceholder": "Enter description", "submit": "Transfer Credits", "processing": "Processing...", "success": "Successfully transferred {{amount}} credits. Remaining: {{remaining}}", "confirm": "Are you sure you want to transfer {{amount}} credits to wallet {{wallet}}?", "errorNoWallet": "Please enter a target wallet address", "errorInvalidAmount": "Please enter a valid amount (positive integer)", "errorGeneral": "Failed to transfer credits", "defaultDescription": "Wallet credits transfer"}}}, "linkAccounts": "Link Accounts", "linkAccountsDesc": "Connect additional login methods to your account for easier access", "linkAccountsDescWeb3": "Connect your social media accounts to your wallet for easier access", "currentProvider": "Current login method", "connectProvider": "Connect this login method", "connected": "Connected", "connect": "Connect", "unknown": "Unknown", "showPanel": "Show Panel", "hidePanel": "Hide Panel", "tabs": {"profile": "Profile", "accounts": "Linked Accounts"}, "stakeCfx": "Stake CFX", "staking": {"title": "CFX Staking", "description": "Stake CFX to gain code audit privileges. The more you stake, the higher your priority", "yourStake": "Your Stake", "totalStaked": "Total Staked", "vaultBalance": "<PERSON><PERSON> Balance", "totalStakers": "Total Stakers", "stakeMore": "Stake", "unstake": "Unstake", "amount": "Amount", "confirm": "Confirm", "cancel": "Cancel", "priority": "Current Priority", "priorityLevels": {"none": "No Access", "low": "Low Priority", "medium": "Medium Priority", "high": "High Priority"}, "readyToStake": "Ready to Stake", "accountCreationInfo": "Your stake account will be created automatically when you make your first stake.", "stakingInformation": "Staking Information", "stakingInfoDescription": "Stake your CFX tokens to participate in the Chain-Fox ecosystem and earn rewards.", "lockDuration": "Lock Duration", "approximatelyMinutes": "Approximately {{minutes}} minutes", "startStaking": "Start Staking", "requestWithdrawal": "UnStake", "withdrawTokens": "With<PERSON>w <PERSON>kens", "stakeCfxTokens": "Stake CFX Tokens", "staking": "Staking...", "processing": "Processing...", "insufficientBalance": "Insufficient Balance", "insufficientCfxBalance": "⚠️ Insufficient CFX balance. Please add CFX tokens to your wallet first.", "minimumStake": "Minimum stake: 10,000 CFX", "withdrawal": {"requested": "UnStaked", "pending": "Your withdrawal request is pending. Tokens will be available after the lock period.", "timeRemaining": "Time Remaining:", "available": "Withdrawal is now available!", "availableNow": "Available now", "conditionsNotMet": "Withdrawal conditions not met, please check account status", "alreadyRequested": "<PERSON><PERSON><PERSON> already requested, please wait for lock period to end"}, "withdrawing": "Withdrawing...", "withdrawAmount": "Withdraw {{amount}} CFX", "lockPeriodRemaining": "Lock period remaining: {{time}}", "points": {"title": "Credits Rewards", "description": "Earn Credits by staking CFX. Credits can be used to boost audit priority", "yourPoints": "Your Credits", "rate": "Reward Rate", "perBlock": "Per Block", "perDay": "Per Day", "history": "Credits History", "earned": "Earned", "used": "Used", "balance": "Balance", "lastUpdated": "Last Updated", "viewAll": "View All", "recentActivity": "Recent Activity", "activities": {"earned": "Credits Earned", "used": "Credits Used", "stakeChanged": "Stake Changed"}}}, "features": {"title": "Features", "welcome": "Select a Feature", "selectPrompt": "Please select a feature from the left panel to get started."}, "credits": {"title": "Credits", "amount": "Amount", "transfer": {"title": "Transfer Credits", "show": "Transfer Credits", "hide": "Hide Form", "method": "Transfer Method", "methodUserId": "UID", "methodWallet": "Wallet Address", "targetUserId": "Target UID", "targetUserIdPlaceholder": "Enter UID", "amount": "Amount", "amountPlaceholder": "Enter amount", "description": "Description (Optional)", "descriptionPlaceholder": "Enter description", "submit": "Transfer Credits", "processing": "Processing...", "success": "Successfully transferred {{amount}} credits. Remaining: {{remaining}}", "confirm": "Are you sure you want to transfer {{amount}} credits to user {{userId}}?", "errorNoUserId": "Please enter a target UID", "errorInvalidAmount": "Please enter a valid amount (positive integer)", "errorGeneral": "Failed to transfer credits", "defaultDescription": "Credits transfer"}}, "points": {"balance": "Credits", "refresh": "Refresh Credits", "refreshed": "Credits refreshed", "refreshError": "Failed to refresh Credits", "history": "Credits History", "noHistory": "No Credits history found", "currentBalance": "Current Balance", "viewReportCost": "Viewing report details costs {{cost}} Credits.", "submitRepositoryCost": "Submitting each repository costs {{cost}} Credits.", "confirmDeduction": "Viewing this report will cost {{points}} Credits. Do you want to continue?", "confirmSubmissionDeduction": "Submitting {{count}} repositories will cost {{points}} Credits. Do you want to continue?", "deductSuccess": "Deducted {{points}} Credits. Remaining: {{remaining}} Credits", "deductError": "Failed to deduct Credits", "insufficientPoints": "Insufficient Credits", "generalError": "An error occurred while processing your request", "checking": "Checking Credits...", "checkSubmitterError": "Error checking user permissions", "userId": {"copy": "Copy UID", "copied": "UID copied to clipboard"}, "transfer": {"title": "Transfer Credits to Another User", "show": "Transfer Credits", "hide": "Hide Transfer Form", "targetUserId": "Target UID", "targetUserIdPlaceholder": "Enter UID", "amount": "Amount", "amountPlaceholder": "Enter amount", "description": "Description (Optional)", "descriptionPlaceholder": "Enter description", "submit": "Transfer Credits", "processing": "Processing...", "success": "Successfully transferred {{amount}} Credits. Remaining: {{remaining}}", "confirm": "Are you sure you want to transfer {{amount}} Credits to user {{userId}}?", "errorNoUserId": "Please enter a target UID", "errorInvalidAmount": "Please enter a valid amount (positive integer)", "errorGeneral": "Failed to transfer Credits", "defaultDescription": "Credits transfer"}, "transaction": {"date": "Date", "amount": "Amount", "type": "Type", "description": "Description", "balance": "Balance After"}, "types": {"view_report": "View Report", "submit_repository": "Submit Repository", "admin_adjustment": "Admin Adjustment", "transfer_in": "Credits Received", "transfer_out": "Credits Sent"}}, "time": {"immediate": "Available now", "daysHours": "{{days}} days {{hours}} hours", "days": "{{days}} days", "hoursMinutes": "{{hours}} hours {{minutes}} minutes", "hours": "{{hours}} hours", "minutes": "{{minutes}} minutes", "seconds": "{{seconds}} seconds", "minutesSeconds": "{{minutes}} minutes {{seconds}} seconds", "hoursMinutesSeconds": "{{hours}} hours {{minutes}} minutes {{seconds}} seconds", "daysMinutesSeconds": "{{days}} days {{minutes}} minutes {{seconds}} seconds", "daysHoursMinutesSeconds": "{{days}} days {{hours}} hours {{minutes}} minutes {{seconds}} seconds", "lessThanMinute": "Less than 1 minute"}, "backToProfile": "Back to Profile"}