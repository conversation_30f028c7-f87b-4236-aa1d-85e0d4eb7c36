{"pageTitle": "Chain-Fox DAO", "pageDescription": "Participate in project governance and decision making by staking CFX tokens to become a DAO member", "welcomeDescription": "Participate in project governance and decision making by staking CFX tokens to become a DAO member and help build Chain-Fox's decentralized future", "enterDao": "Learn About DAO", "returnToWelcome": "Return to Welcome Page", "developerInfo": "Technical support by community dev 1379hash", "devAddress": "Tip address:", "copyAddress": "Copy Address", "daoProgress": "Completion", "progressStages": {"NOT_STARTED": "Not Started", "PLANNING": "Planning Phase", "DESIGN": "Design Phase", "DEVELOPMENT": "Development Phase", "TESTING": "Testing Phase", "REVIEW": "Review Phase", "COMPLETED": "Completed"}, "walletSectionTitle": "Connect Wallet", "walletConnectionDescription": "Connect your Solana wallet to participate in Chain-Fox DAO governance and staking. You need to have SOL for transaction fees and CFX tokens for staking.", "walletConnected": "Wallet Connected", "walletNotConnected": "Please connect your wallet to continue", "walletReadyForDao": "Your wallet is ready to participate in DAO activities", "introTitle": "What is Chain-Fox DAO?", "introDescription": "Chain-Fox DAO is a decentralized autonomous organization that empowers community members to participate in project governance and decision-making through a staking mechanism. Users can become DAO members by locking (staking) CFX tokens, gaining voting rights, code audit rights, and other community benefits.", "communityGovernance": "Community Governance", "communityGovernanceDesc": "Participate in proposal voting, directly influencing project direction and major decisions.", "codeAudit": "Code Audit Rights", "codeAuditDesc": "Gain the right to participate in protocol code audits, ensuring project security and transparency.", "stakingRewards": "Staking Rewards", "stakingRewardsDesc": "Receive liquidity yields, partner airdrops, and other DAO ecosystem benefits.", "tokenInfo": "Token Information", "tokenInfoDesc": "CFX Token Address:", "contractInfo": "Stake Contract Address:", "inDevelopment": "In Development", "stakingProcessTitle": "Staking Process", "stakingProcessDesc": "Chain-Fox DAO staking protocol is a decentralized application (DApp) running on the Solana blockchain, allowing users to stake CFX tokens to gain governance rights and potential rewards.", "stakeTab": "Stake", "unstakeTab": "Unstake", "preparationTitle": "Preparation", "preparation1": "Have a Solana wallet (such as <PERSON>, <PERSON><PERSON><PERSON><PERSON>, etc.)", "preparation2": "Hold a certain amount of SOL (for transaction fees)", "preparation3": "Hold CFX tokens (for staking)", "stakingStepsTitle": "Staking Steps", "step1Title": "Connect Wallet", "step1Desc": "Visit the Chain-Fox DAO platform and connect your Solana wallet.", "step2Title": "Create Staking Account", "step2Desc": "For first-time users, the system will create a dedicated staking account for you.", "step3Title": "Select Staking Amount", "step3Desc": "Enter the amount of CFX tokens you want to stake.", "step4Title": "Confirm Transaction", "step4Desc": "Confirm the staking transaction in your wallet.", "step5Title": "Complete Staking", "step5Desc": "After transaction confirmation, your tokens will be locked in the staking pool.", "stakingNote": "Staking Note", "stakingNoteDesc": "The minimum staking amount is 10,000 CFX tokens. After staking, you will immediately receive voting weight proportional to your staking amount.", "unstakingStepsTitle": "Unstaking Process", "unstakeStep1Title": "Request Withdrawal", "unstakeStep1Desc": "Apply for unstaking on the platform.", "unstakeStep2Title": "Wait for Lock Period", "unstakeStep2Desc": "The system will start a lock period timer (default is 30 days).", "unstakeStep3Title": "With<PERSON>w <PERSON>kens", "unstakeStep3Desc": "After the lock period ends, you can withdraw your tokens and earned rewards.", "lockPeriodNote": "Lock Period Information", "lockPeriodNoteDesc": "When you apply for unstaking, the system will start a lock period timer (default 30 days), during which tokens cannot be withdrawn.", "importantNote": "Important Note", "importantNoteDesc": "You can increase your staking amount during the lock period, but this will reset your lock period.", "governanceTitle": "DAO Voting Governance Mechanism", "governanceDesc": "Chain-Fox DAO employs a decentralized voting mechanism, allowing CFX holders to directly participate in project decisions and development direction.", "proposalTypesTab": "Proposal Types", "proposalProcessTab": "Proposal Process", "votingMechanismTab": "Voting Mechanism", "proposalTypesTitle": "Proposal Types", "parameterAdjustmentTitle": "Protocol Parameter Adjustment Proposals", "fundingProposalTitle": "Funding Allocation Proposals", "techUpgradeTitle": "Technical Upgrade Proposals", "communityInitiativeTitle": "Community Initiative Proposals", "governanceExamplesTitle": "DAO Governance Examples", "proposalTypeHeader": "Proposal Type", "exampleHeader": "Example", "impactHeader": "Impact Range", "parameterAdjustment1": "Modify staking lock period", "parameterAdjustment2": "Adjust liquidity allocation ratio", "parameterAdjustment3": "Update reward calculation formula", "fundingProposal1": "Determine the direction of team fund usage", "fundingProposal2": "Approve ecosystem development fund allocation", "fundingProposal3": "Vote on partnership relationships", "techUpgrade1": "Vote on development priorities for new features", "techUpgrade2": "Approve major technical architecture changes", "techUpgrade3": "Decide on integration of new blockchains or protocols", "communityInitiative1": "Marketing campaigns", "communityInitiative2": "Community events and reward programs", "communityInitiative3": "Education and training projects", "parameterAdjustmentType": "Protocol Parameter Adjustment", "parameterAdjustmentExample": "Change lock period from 30 days to 15 days", "parameterAdjustmentImpact": "All staking users", "fundingType": "Funding Allocation", "fundingExample": "Allocate 1 million CFX for marketing", "fundingImpact": "Project development and visibility", "techUpgradeType": "Technical Upgrade", "techUpgradeExample": "Integrate new DEX protocol to improve liquidity yield", "techUpgradeImpact": "Reward rates and system stability", "communityType": "Community Initiative", "communityExample": "Launch developer incentive program", "communityImpact": "Ecosystem expansion", "proposalProcessTitle": "Proposal Process", "proposalCreationTitle": "Proposal Creation", "proposalCreation1": "Any user staking over 100,000 CFX can create proposals", "proposalCreation2": "Proposals must include detailed description, objectives, and implementation plan", "proposalCreation3": "Creating a proposal requires a small fee to prevent spam proposals", "discussionPeriodTitle": "Discussion Period", "discussionPeriod1": "After creation, proposals enter a 7-day discussion period", "discussionPeriod2": "Community members can discuss proposals on forums and social media", "discussionPeriod3": "Proposal creators can modify details based on feedback", "votingPeriodTitle": "Voting Period", "votingPeriod1": "After the discussion period, a 5-day voting period begins", "votingPeriod2": "All staking users can vote according to their staking weight", "votingPeriod3": "Voting options include: For, Against, Abstain", "resultCalculationTitle": "Result Calculation", "resultCalculation1": "Results are automatically calculated after the voting period ends", "resultCalculation2": "Proposals must meet two conditions to pass:", "resultCalculation2a": "For votes exceed 66% of total votes", "resultCalculation2b": "Voting participation exceeds 40% of total staked amount", "executionPeriodTitle": "Execution Period", "executionPeriod1": "Passed proposals enter the execution period", "executionPeriod2": "Technical team implements changes based on proposal content", "executionPeriod3": "Implementation progress is transparent on the DAO platform", "implementationTitle": "Implementation", "implementation1": "Execute specific implementation based on proposal content", "implementation2": "Publish regular implementation progress reports", "implementation3": "Release final implementation report upon completion", "stakingDurationFactorTitle": "Staking Duration Factor:", "stakingDurationHeader": "Staking Duration", "durationFactorHeader": "Duration Factor", "stakingDurationNote": "This means long-term stakers have greater say in governance decisions, encouraging users to hold long-term and participate in project governance.", "votingIncentivesTitle": "Voting Incentive Mechanism", "votingIncentivesDesc": "To encourage active participation in governance voting, Chain-Fox DAO has established a voting incentive mechanism:", "votingRewardTitle": "Voting Rewards", "votingRewardDesc": "Users who participate in voting will receive additional reward Credits, automatically calculated and distributed", "proposalCreationRewardTitle": "Proposal Creation Rewards", "proposalCreationRewardDesc": "Creators of successfully passed proposals will receive special rewards sent directly to their wallets", "activityBonusTitle": "Activity Bonus", "activityBonusDesc": "Users who vote consecutively will receive an activity bonus, increasing their reward ratio, with data stored in their on-chain accounts", "participationGuide": "Participation Guide", "participationGuideDesc": "How to participate in DAO governance: After staking CFX, you will automatically receive voting rights proportional to your staking amount. You can participate in proposal voting and discussion on the 'Governance' page of the Chain-Fox DAO platform.", "votingWeightTitle": "Voting Weight Calculation", "votingWeightDesc": "Voting weight is based on the user's staking amount and staking duration, calculated as follows:", "votingWeightFormula": "Voting Weight = Staking Amount × (1 + Staking Duration Factor)", "rewardsTitle": "Staking Benefits and Reward Mechanism", "rewardsDesc": "The main purpose of the Chain-Fox DAO staking protocol is to establish a stable token holder community and provide specific benefits and rewards to staking users. Unlike traditional DeFi mining, CFX tokens have been fully released through pump.fun (total supply 1 billion), with 5% locked by the team, so the reward mechanism uses special methods.", "stakingBenefitsTitle": "Staking Benefits", "rewardSourcesTitle": "Reward Sources", "liquidityManagementTitle": "Liquidity Management Mechanism", "expectedRewardsTitle": "Expected Reward Rates", "rewardDistributionTitle": "Reward Distribution", "auditRightTitle": "Code Audit Rights", "auditRightDesc": "Staking users will gain the right to participate in protocol code audits and governance proposals", "decisionRightTitle": "Community Decision Rights", "decisionRightDesc": "Voting weight for future protocol development directions is proportional to staking amount", "earlyAccessTitle": "Early Feature Access", "earlyAccessDesc": "Priority access to new features and services in the Chain-Fox ecosystem", "liquidityRewardTitle": "Liquidity Yield", "liquidityRewardDesc": "Staked CFX tokens will be partially used to provide liquidity on decentralized exchanges, with transaction fees returned to users as rewards", "teamIncentiveTitle": "Team Incentives", "teamIncentiveDesc": "The project team will periodically inject a certain amount of CFX tokens into the staking pool as additional staking rewards", "ecosystemIncomeTitle": "Ecosystem Income", "ecosystemIncomeDesc": "A portion of the income generated by the Chain-Fox DAO ecosystem will be distributed to staking users", "partnerAirdropTitle": "Partner Airdrops", "partnerAirdropDesc": "Projects partnering with Chain-Fox DAO may provide exclusive airdrops to staking users", "liquidityManagementDesc": "To balance liquidity yield and prevent run risks, Chain-Fox DAO adopts the following mechanisms:", "liquidityAllocationTitle": "Liquidity Allocation Ratio", "reservedForStaking": "Reserved in the staking pool, ensuring users can withdraw at any time", "usedForLiquidity": "Used to provide liquidity, generating yield", "dynamicAdjustmentTitle": "Dynamic Adjustment Mechanism", "dynamicAdjustmentDesc": "The system dynamically adjusts the liquidity allocation ratio based on staking pool usage. When withdrawal requests increase, the system automatically reduces the liquidity allocation ratio; when the staking pool stabilizes, the system gradually restores the liquidity allocation ratio.", "emergencyReserveTitle": "Emergency Reserve", "emergencyReserveDesc": "The team maintains a certain percentage of CFX as an emergency reserve to respond to extreme market conditions", "expectedRewardsDesc": "The following reward rates are for reference only, actual rewards will depend on team token injections and ecosystem income:", "stakingDuration": "Staking Duration", "basicRights": "Basic Rights", "potentialReturn": "Potential Annual Return", "notes": "Notes", "basicAuditRight": "Basic Audit Rights", "standardAuditRight": "Standard Audit Rights", "advancedAuditRight": "Advanced Audit Rights", "coreMemberRight": "Core Member Rights", "mainlyGovernance": "Mainly governance rights", "dependsOnTeam": "Depends on team injections", "includesAirdrop": "Includes ecosystem airdrops", "priorityAccess": "Priority access to new projects", "rewardsDisclaimer": "Reward rates are for reference only and may change based on market conditions and project development. Long-term stakers will receive more rights and higher reward rates.", "rewardDistributionDesc": "Rewards are distributed based on the following factors:", "stakingAmount": "Staking Amount", "stakingAmountDesc": "The more CFX tokens staked, the larger the reward share", "participation": "Participation", "participationDesc": "User activity level in community governance affects reward ratio", "stakingDurationDesc": "The longer tokens are locked, the more rewards and rights received", "rewardFormula": "Reward Calculation Formula:", "durationPeriod1": "1-30 days", "durationPeriod2": "31-90 days", "durationPeriod3": "91-180 days", "durationPeriod4": ">180 days", "rewardFormulaNote": "Actual distribution will be automatically executed by smart contracts, ensuring a fair and transparent process", "faqTitle": "Frequently Asked Questions", "minStakingQuestion": "Is there a minimum staking amount requirement?", "minStakingAnswer": "Yes, the minimum staking amount is 10,000 CFX tokens.", "viewStakingQuestion": "How do I check my staking status and voting rights?", "viewStakingAnswer": "After connecting your wallet, you can check your staking status, voting weight, rewards, and unlock time on the 'My Staking' page of the Chain-Fox DAO platform.", "lockPeriodQuestion": "Can I increase my staking amount during the lock period?", "lockPeriodAnswer": "Yes. Even if you have already applied for unstaking and are in the lock period, you can still increase your staking amount, but this will reset your lock period.", "rewardDistributionQuestion": "How are staking rewards distributed?", "rewardDistributionAnswer": "Rewards will be distributed periodically based on liquidity yields and team injections. When you withdraw your staked tokens, accumulated rewards will be sent to your wallet together.", "liquidityUseQuestion": "How are my staked tokens used to provide liquidity?", "liquidityUseAnswer": "A portion of your staked tokens (about 40%) will be used to provide liquidity in decentralized exchanges in the Solana ecosystem, and the transaction fees generated will be returned to all staking users as rewards.", "governanceParticipationQuestion": "How do I participate in DAO governance?", "governanceParticipationAnswer": "After staking CFX, you will automatically receive voting rights proportional to your staking amount. You can participate in proposal voting and discussion on the 'Governance' page of the Chain-Fox DAO platform.", "stakingSafetyQuestion": "Is staking safe?", "stakingSafetyAnswer": "Yes, Chain-Fox DAO's staking protocol is self-developed by the team and has undergone comprehensive code audits, ensuring the protocol's security and reliability.", "contractStatusQuestion": "When will the Stake contract be launched?", "contractStatusAnswer": "The Stake contract is currently under development and is expected to be completed and deployed to the Solana mainnet soon. The exact launch time will be announced through official channels.", "stakeVsUnstake": "Staking vs. Unstaking Comparison", "feature": "Feature", "staking": "Staking", "unstaking": "Unstaking", "processingTime": "Processing Time", "immediate": "Immediate", "waitPeriod": "30-day lock period required", "transactionFee": "Transaction Fee", "low": "Low", "minAmount": "Minimum Amount", "noLimit": "No minimum limit", "governanceRights": "Governance Rights", "immediateEffect": "Effective immediately after staking", "loseAfterRequest": "Lost after unstaking request", "rewardCalculation": "Reward Calculation", "baseOnLiquidity": "Based on liquidity yield and team injections", "stopAfterRequest": "Stops after unstaking request", "moreQuestions": "If you have more questions, please contact our community administrators or visit the official Discord channel.", "disclaimer": "Disclaimer: This document is for reference only and does not constitute investment advice.", "conclusionTitle": "Join <PERSON>-Fox DAO", "conclusion": "By staking CFX tokens, become a member of Chain-Fox DAO, participate in project governance and earn corresponding benefits. We welcome every community member to actively participate and build Chain-Fox's decentralized future together.", "user": "User", "platform": "Platform", "account": "Account", "amount": "Amount", "confirm": "Confirm", "lock": "Lock", "active": "Active", "connectWallet": "Connect Wallet", "firstUse": "First Use", "existingAccount": "Existing Account", "selectAmount": "Select Amount", "confirmTx": "Confirm Transaction", "startReward": "Start Reward Calculation", "waitPeriod30": "Wait 30 Days", "extract": "Extract", "wallet": "Wallet", "tokens": "Tokens", "rewards": "Rewards", "requestUnstake": "Request Unstake", "lockPeriod": "Lock Period", "txConfirm": "Transaction Confirmed", "returnToWallet": "Return to Wallet", "rewardDistribution": "Reward Distribution", "proposalCreation": {"create": "Proposal Creation", "discuss": "Discussion Period", "vote": "Voting Period", "result": "Result Calculation", "execute": "Execution Period", "archive": "Proposal Archive", "implement": "Implementation", "minStake": "Minimum Stake Required", "days7": "7 Days", "days5": "5 Days", "pass": "Passed", "notPass": "Not Passed"}, "formula": {"where": "Where:", "whereL": "Where:", "userReward": "Reward of user i", "userStake": "Staked amount of user i", "totalStake": "Total staked amount of all users", "rewardPool": "Reward pool size", "durationFactor": "Duration factor based on staking duration", "activityFactor": "Activity factor based on user participation", "tRepresents": "t<sub>i</sub> = number of days user i has staked", "liquidityRatio": "Liquidity Ratio", "baseRatio": "Base Ratio", "adjustFactor": "Adjustment factor (default 0.8)", "withdrawalRequests": "Current total withdrawal requests", "totalStakePool": "Total stake pool amount", "maxRatio": "Maximum liquidity ratio (40%)", "minRatio": "Minimum liquidity ratio (5%)", "formulaExplanation": "R<sub>i</sub> = Reward, S<sub>i</sub> = Staked amount, P = Pool size, D<sub>i</sub> = Duration factor, A<sub>i</sub> = Activity factor", "liquidityExplanation": "L = Liquidity ratio, α = Adjustment factor, W<sub>req</sub> = Withdrawal requests, W<sub>total</sub> = Total stake", "reward": "<PERSON><PERSON>", "stakeRatio": "Stake Rat<PERSON>", "stakeDays1": "Staking Days", "days": " days", "withdrawalRatio": "Withdrawal Request Ratio", "adjustmentRange": "Adjustment Range", "baseRatioLabel": "Base Ratio", "adjustmentRangeLabel": "Adjustment Range", "adjustFactorLabel": "Adjustment Factor"}, "durationFactorFormula": "Duration Factor Calculation:", "liquidityAdjustmentFormula": "Dynamic Liquidity Adjustment Formula:", "readyToStake": "Ready to Start Staking?", "stakingCallToAction": "Visit our dedicated staking page to connect your wallet and start staking CFX tokens.", "goToStaking": "Go to Staking Page"}