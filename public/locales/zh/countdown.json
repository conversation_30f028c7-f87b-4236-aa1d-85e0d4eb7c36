{"title": "敬请期待", "comingSoon": "我们将于2025年5月6日发布产品！", "syncError": "同步倒计时失败", "syncFallback": "使用本地时间作为备用", "days": "天", "hours": "小时", "minutes": "分钟", "seconds": "秒", "stayTuned": "请持续关注更新，成为第一批体验新功能的用户！", "followUs": "在X上关注我们", "productHighlights": "产品亮点", "aiAudit": "智能代码审计", "aiAuditDesc": "基于AI的智能代码审计，快速发现安全漏洞", "multiChain": "多链支持", "multiChainDesc": "支持多种区块链平台的智能合约审计", "detailedReports": "详细报告", "detailedReportsDesc": "生成专业的安全审计报告，包含详细的问题分析", "realTimeFeedback": "实时反馈", "realTimeFeedbackDesc": "实时查看审计进度和结果，提高开发效率", "freeAccess": "首发版本将提供免费体验，敬请期待！", "tryNow": "立即体验", "showCountdown": "显示倒计时", "showFeatures": "显示功能介绍", "auditSolutions": "智能合约安全审计解决方案", "auditSolutionsDesc": "链狐提供全面的 Solana 智能合约安全审计服务，通过两种方式帮助开发者识别和修复潜在的安全漏洞。", "selfAudit": {"title": "自助审计", "description": "通过我们的自助审计系统，开发者可以直接提交 GitHub 仓库链接，系统将自动分析代码并生成详细的安全报告。", "feature1": "提交 GitHub 仓库链接，系统自动检测 Solana 智能合约代码", "feature2": "AI 辅助分析，实时显示检测过程和结果", "feature3": "生成详细的安全报告，包含漏洞分析和修复建议", "button": "开始自助审计"}, "sampleAudit": {"title": "抽样审计", "description": "我们的专业团队定期对提交的项目进行深入审计，提供更全面的安全评估和专业建议。", "feature1": "专业团队对提交的项目进行深入审计", "feature2": "命令行风格的详细问题报告，清晰展示每个文件中的安全问题", "feature3": "提供专业的安全评估和修复建议", "button": "查看审计报告"}}