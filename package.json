{"name": "chain-fox-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@solana/wallet-adapter-backpack": "^0.1.14", "@solana/wallet-adapter-base": "^0.9.26", "@solana/wallet-adapter-react": "^0.15.38", "@solana/wallet-adapter-react-ui": "^0.9.38", "@solana/wallet-adapter-wallets": "^0.19.36", "@solana/web3.js": "^1.98.2", "@supabase/supabase-js": "^2.49.4", "autoprefixer": "^10.4.16", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "framer-motion": "^10.18.0", "gsap": "^3.12.7", "html2canvas": "^1.4.1", "i18next": "^25.0.1", "i18next-browser-languagedetector": "^8.0.5", "i18next-http-backend": "^3.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "postcss": "^8.4.31", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.1", "react-router-dom": "^7.5.2", "react-to-pdf": "^2.0.0", "tailwindcss": "^3.3.3", "three": "^0.176.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "vite": "^4.4.5"}}